<!DOCTYPE html>
<html><head><title>🌍 Epoch of Elria - 3D Game Engine with Dream Weaver Characters</title>
<style>
body{margin:0;background:linear-gradient(135deg,#0c0c0c 0%,#1a1a2e 50%,#16213e 100%);font-family:'Courier New',monospace;color:#fff;height:100vh;overflow:hidden}
.container{display:flex;flex-direction:column;height:100vh}
.header{background:rgba(0,0,0,0.9);padding:15px;text-align:center;border-bottom:3px solid #4A90E2;box-shadow:0 2px 10px rgba(74,144,226,0.3)}
.title{font-size:28px;color:#4A90E2;margin-bottom:5px;text-shadow:0 0 10px rgba(74,144,226,0.5)}
.subtitle{font-size:14px;color:#FFD700;margin-bottom:5px}
.world{flex:1;position:relative;background:radial-gradient(ellipse at center,#001122 0%,#000011 50%,#000000 100%);perspective:1000px;overflow:hidden}

/* 3D Scene Styles */
.scene-3d{position:absolute;width:100%;height:100%;transform-style:preserve-3d;animation:scene-rotate 60s linear infinite}
@keyframes scene-rotate{from{transform:rotateY(0deg)}to{transform:rotateY(360deg)}}

/* SVG Texture Definitions */
.svg-earth{background:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cdefs%3E%3CradialGradient id='earth'%3E%3Cstop offset='0%25' stop-color='%234A90E2'/%3E%3Cstop offset='70%25' stop-color='%232E5BBA'/%3E%3Cstop offset='100%25' stop-color='%231E3A8A'/%3E%3C/radialGradient%3E%3C/defs%3E%3Ccircle cx='50' cy='50' r='45' fill='url(%23earth)'/%3E%3Cpath d='M20,30 Q40,25 60,35 Q80,45 70,65 Q50,75 30,65 Q10,55 20,30' fill='%23228B22' opacity='0.8'/%3E%3Cpath d='M65,20 Q85,30 80,50 Q75,70 55,60 Q35,50 45,30 Q55,10 65,20' fill='%23228B22' opacity='0.8'/%3E%3C/svg%3E")}

.svg-platform{background:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 60 40'%3E%3Cdefs%3E%3ClinearGradient id='stone' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23A0522D'/%3E%3Cstop offset='50%25' stop-color='%238B4513'/%3E%3Cstop offset='100%25' stop-color='%23654321'/%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='60' height='40' fill='url(%23stone)' rx='5'/%3E%3Cpath d='M5,5 L55,5 L50,35 L10,35 Z' fill='%23D2691E' opacity='0.3'/%3E%3C/svg%3E")}

.svg-crystal{background:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 40 40'%3E%3Cdefs%3E%3CradialGradient id='crystal'%3E%3Cstop offset='0%25' stop-color='%23FFFF00'/%3E%3Cstop offset='50%25' stop-color='%23FFD700'/%3E%3Cstop offset='100%25' stop-color='%23FFA500'/%3E%3C/radialGradient%3E%3C/defs%3E%3Cpolygon points='20,5 35,20 20,35 5,20' fill='url(%23crystal)'/%3E%3Cpolygon points='20,5 35,20 20,20' fill='%23FFFF00' opacity='0.7'/%3E%3C/svg%3E")}

/* Character Styles with SVG Textures */
.character{position:absolute;width:80px;height:100px;cursor:pointer;transition:all 0.3s ease;transform-style:preserve-3d}
.character:hover{transform:scale(1.1) translateZ(10px);filter:drop-shadow(0 0 15px rgba(255,255,255,0.5))}

.xing{background:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 80 100'%3E%3Cdefs%3E%3ClinearGradient id='xing-aura' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23CC99FF'/%3E%3Cstop offset='50%25' stop-color='%239966FF'/%3E%3Cstop offset='100%25' stop-color='%236633CC'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cellipse cx='40' cy='85' rx='35' ry='8' fill='url(%23xing-aura)' opacity='0.3'/%3E%3Ccircle cx='40' cy='25' r='15' fill='%23FFDBAC'/%3E%3Crect x='30' y='35' width='20' height='40' fill='%239966FF' rx='3'/%3E%3Cpath d='M25,45 Q40,40 55,45 Q50,65 40,70 Q30,65 25,45' fill='%23CC99FF'/%3E%3Ctext x='40' y='95' text-anchor='middle' fill='%23FFFFFF' font-size='8'%3EXing%3C/text%3E%3C/svg%3E");animation:float 3s ease-in-out infinite}

.xerx{background:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 80 100'%3E%3Cdefs%3E%3ClinearGradient id='xerx-aura'%3E%3Cstop offset='0%25' stop-color='%23FF6B6B'/%3E%3Cstop offset='50%25' stop-color='%23FF4444'/%3E%3Cstop offset='100%25' stop-color='%23CC0000'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cellipse cx='40' cy='85' rx='35' ry='8' fill='url(%23xerx-aura)' opacity='0.3'/%3E%3Ccircle cx='40' cy='25' r='15' fill='%23FFDBAC'/%3E%3Crect x='30' y='35' width='20' height='40' fill='%23FF4444' rx='3'/%3E%3Cpath d='M20,50 L35,45 L40,55 L45,45 L60,50 L50,70 L30,70 Z' fill='%23FF6B6B'/%3E%3Ctext x='40' y='95' text-anchor='middle' fill='%23FFFFFF' font-size='8'%3EXerx%3C/text%3E%3C/svg%3E");animation:float 3s ease-in-out infinite 0.5s}

.heart{background:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 80 100'%3E%3Cdefs%3E%3CradialGradient id='heart-aura'%3E%3Cstop offset='0%25' stop-color='%23FF69B4'/%3E%3Cstop offset='50%25' stop-color='%23FF1493'/%3E%3Cstop offset='100%25' stop-color='%23DC143C'/%3E%3C/radialGradient%3E%3C/defs%3E%3Cellipse cx='40' cy='85' rx='35' ry='8' fill='url(%23heart-aura)' opacity='0.3'/%3E%3Cpath d='M40,30 C35,20 20,20 20,35 C20,50 40,65 40,65 C40,65 60,50 60,35 C60,20 45,20 40,30 Z' fill='%23FF1493'/%3E%3Ccircle cx='40' cy='75' r='8' fill='%23FF69B4'/%3E%3Ctext x='40' y='95' text-anchor='middle' fill='%23FFFFFF' font-size='8'%3EHeart%3C/text%3E%3C/svg%3E");animation:pulse 2s ease-in-out infinite}

.lyra{background:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 80 100'%3E%3Cdefs%3E%3ClinearGradient id='lyra-aura'%3E%3Cstop offset='0%25' stop-color='%23E6FFE6'/%3E%3Cstop offset='50%25' stop-color='%23CCFFCC'/%3E%3Cstop offset='100%25' stop-color='%2399FF99'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cellipse cx='40' cy='85' rx='35' ry='8' fill='url(%23lyra-aura)' opacity='0.3'/%3E%3Ccircle cx='40' cy='25' r='15' fill='%23FFDBAC'/%3E%3Crect x='30' y='35' width='20' height='40' fill='%23CCFFCC' rx='3'/%3E%3Cpath d='M25,40 Q40,35 55,40 L50,45 Q40,42 30,45 Z' fill='%2399FF99'/%3E%3Ccircle cx='30' cy='50' r='3' fill='%23FFFFFF'/%3E%3Ccircle cx='40' cy='52' r='3' fill='%23FFFFFF'/%3E%3Ccircle cx='50' cy='50' r='3' fill='%23FFFFFF'/%3E%3Ctext x='40' y='95' text-anchor='middle' fill='%23FFFFFF' font-size='8'%3ELyra%3C/text%3E%3C/svg%3E");animation:melody 4s ease-in-out infinite}

.the-one{background:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 80 100'%3E%3Cdefs%3E%3CradialGradient id='one-aura'%3E%3Cstop offset='0%25' stop-color='%23000000'/%3E%3Cstop offset='50%25' stop-color='%23330000'/%3E%3Cstop offset='100%25' stop-color='%23660000'/%3E%3C/radialGradient%3E%3C/defs%3E%3Cellipse cx='40' cy='85' rx='35' ry='8' fill='url(%23one-aura)' opacity='0.5'/%3E%3Cpath d='M40,10 L50,30 L70,30 L55,45 L60,65 L40,55 L20,65 L25,45 L10,30 L30,30 Z' fill='%23660000'/%3E%3Ccircle cx='40' cy='35' r='8' fill='%23FF0000'/%3E%3Ctext x='40' y='95' text-anchor='middle' fill='%23FFFFFF' font-size='8'%3EThe One%3C/text%3E%3C/svg%3E");animation:menace 5s ease-in-out infinite}

/* Animations */
@keyframes float{0%,100%{transform:translateY(0px)}50%{transform:translateY(-10px)}}
@keyframes pulse{0%,100%{transform:scale(1)}50%{transform:scale(1.05)}}
@keyframes melody{0%,100%{transform:translateY(0px) rotate(0deg)}25%{transform:translateY(-5px) rotate(2deg)}75%{transform:translateY(-5px) rotate(-2deg)}}
@keyframes menace{0%,100%{transform:scale(1) rotate(0deg);filter:drop-shadow(0 0 10px #660000)}50%{transform:scale(1.1) rotate(5deg);filter:drop-shadow(0 0 20px #FF0000)}}

/* 3D Objects */
.earth{position:absolute;width:150px;height:150px;border-radius:50%;top:50%;left:50%;transform:translate(-50%,-50%) rotateX(15deg);animation:earth-rotate 30s linear infinite;cursor:pointer;box-shadow:0 0 30px rgba(74,144,226,0.5)}
@keyframes earth-rotate{from{transform:translate(-50%,-50%) rotateX(15deg) rotateY(0deg)}to{transform:translate(-50%,-50%) rotateX(15deg) rotateY(360deg)}}

.platform{position:absolute;width:80px;height:50px;border-radius:10px;cursor:pointer;transition:all 0.3s ease;transform-style:preserve-3d}
.platform:hover{transform:scale(1.1) translateZ(5px);box-shadow:0 0 15px rgba(210,105,30,0.7)}

.crystal{position:absolute;width:50px;height:50px;border-radius:50%;cursor:pointer;animation:crystal-sparkle 3s ease-in-out infinite;transform-style:preserve-3d}
@keyframes crystal-sparkle{0%,100%{box-shadow:0 0 15px #FFD700;transform:scale(1) rotate(0deg)}50%{box-shadow:0 0 30px #FFFF00,0 0 40px #FFD700;transform:scale(1.1) rotate(180deg)}}

/* Portal and Special Effects */
.portal{position:absolute;width:100px;height:100px;border-radius:50%;background:conic-gradient(from 0deg,#FF00FF,#00FFFF,#FFFF00,#FF00FF);animation:portal-spin 2s linear infinite;opacity:0.8}
@keyframes portal-spin{from{transform:rotate(0deg)}to{transform:rotate(360deg)}}

/* UI Elements */
.controls{background:rgba(0,0,0,0.95);padding:20px;display:flex;justify-content:space-between;align-items:center;border-top:3px solid #4A90E2;box-shadow:0 -2px 10px rgba(74,144,226,0.3)}
.control-group{display:flex;gap:10px;align-items:center}
button{background:linear-gradient(45deg,#4A90E2,#357ABD);color:white;border:none;padding:12px 18px;border-radius:8px;cursor:pointer;font-family:inherit;font-size:14px;transition:all 0.3s ease;box-shadow:0 2px 5px rgba(0,0,0,0.3)}
button:hover{background:linear-gradient(45deg,#357ABD,#2E5BBA);transform:translateY(-2px);box-shadow:0 4px 10px rgba(0,0,0,0.4)}
.edit-mode{background:linear-gradient(45deg,#FF6B35,#FF4500)!important}
.character-btn{background:linear-gradient(45deg,#9966FF,#6633CC)}
.character-btn:hover{background:linear-gradient(45deg,#6633CC,#4D0099)}

.info{position:absolute;top:20px;right:20px;background:rgba(0,0,0,0.9);padding:20px;border-radius:15px;border:3px solid #8B4513;min-width:250px;box-shadow:0 0 20px rgba(139,69,19,0.3)}
.camera-info{position:absolute;top:20px;left:20px;background:rgba(0,0,0,0.9);padding:20px;border-radius:15px;border:3px solid #FFD700;box-shadow:0 0 20px rgba(255,215,0,0.3)}
.character-panel{position:absolute;bottom:120px;left:20px;background:rgba(0,0,0,0.9);padding:15px;border-radius:15px;border:3px solid #9966FF;max-width:300px;box-shadow:0 0 20px rgba(153,102,255,0.3)}

.notification{position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:rgba(0,0,0,0.95);color:#FFD700;padding:25px 35px;border-radius:15px;border:3px solid #FFD700;z-index:1000;font-size:18px;text-align:center;display:none;box-shadow:0 0 30px rgba(255,215,0,0.5)}

/* Player Character */
.player{position:absolute;width:60px;height:80px;cursor:pointer;transition:all 0.2s ease;transform-style:preserve-3d;z-index:100}
.player-avatar{background:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 60 80'%3E%3Cdefs%3E%3ClinearGradient id='player-body'%3E%3Cstop offset='0%25' stop-color='%234169E1'/%3E%3Cstop offset='50%25' stop-color='%232E5BBA'/%3E%3Cstop offset='100%25' stop-color='%231E3A8A'/%3E%3C/linearGradient%3E%3CradialGradient id='player-aura'%3E%3Cstop offset='0%25' stop-color='%23FFFFFF' opacity='0.8'/%3E%3Cstop offset='100%25' stop-color='%234169E1' opacity='0.3'/%3E%3C/radialGradient%3E%3C/defs%3E%3Cellipse cx='30' cy='75' rx='25' ry='5' fill='url(%23player-aura)'/%3E%3Ccircle cx='30' cy='20' r='12' fill='%23FFDBAC'/%3E%3Crect x='22' y='30' width='16' height='30' fill='url(%23player-body)' rx='3'/%3E%3Crect x='18' y='35' width='8' height='20' fill='url(%23player-body)' rx='2'/%3E%3Crect x='34' y='35' width='8' height='20' fill='url(%23player-body)' rx='2'/%3E%3Crect x='24' y='60' width='6' height='15' fill='%23654321' rx='1'/%3E%3Crect x='30' y='60' width='6' height='15' fill='%23654321' rx='1'/%3E%3Ccircle cx='26' cy='18' r='2' fill='%23000000'/%3E%3Ccircle cx='34' cy='18' r='2' fill='%23000000'/%3E%3Cpath d='M26,24 Q30,26 34,24' stroke='%23000000' stroke-width='1' fill='none'/%3E%3C/svg%3E");width:100%;height:100%;background-size:contain;background-repeat:no-repeat}

.player.walking{animation:walk 0.6s ease-in-out infinite}
.player.running{animation:run 0.4s ease-in-out infinite}
.player.jumping{animation:jump 0.8s ease-out}

@keyframes walk{0%,100%{transform:translateY(0px) rotate(0deg)}25%{transform:translateY(-2px) rotate(1deg)}75%{transform:translateY(-2px) rotate(-1deg)}}
@keyframes run{0%,100%{transform:translateY(0px) scale(1)}50%{transform:translateY(-4px) scale(1.05)}}
@keyframes jump{0%{transform:translateY(0px) scale(1)}50%{transform:translateY(-20px) scale(1.1)}100%{transform:translateY(0px) scale(1)}}

.player-facing-left{transform:scaleX(-1)}
.player-facing-right{transform:scaleX(1)}

/* Player UI */
.player-hud{position:absolute;bottom:140px;right:20px;background:rgba(0,0,0,0.9);padding:15px;border-radius:15px;border:3px solid #4169E1;min-width:200px;box-shadow:0 0 20px rgba(65,105,225,0.3)}
.player-stats{display:flex;flex-direction:column;gap:5px}
.stat-bar{background:rgba(255,255,255,0.2);height:8px;border-radius:4px;overflow:hidden}
.stat-fill{height:100%;transition:width 0.3s ease}
.health-fill{background:linear-gradient(90deg,#FF4444,#FF6666)}
.energy-fill{background:linear-gradient(90deg,#4169E1,#6495ED)}
.experience-fill{background:linear-gradient(90deg,#FFD700,#FFA500)}

/* Movement Trail */
.movement-trail{position:absolute;width:4px;height:4px;background:rgba(65,105,225,0.6);border-radius:50%;pointer-events:none;animation:trail-fade 1s ease-out forwards}
@keyframes trail-fade{from{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(0.5)}}

/* Interaction Indicators */
.interaction-prompt{position:absolute;background:rgba(0,0,0,0.8);color:#FFD700;padding:8px 12px;border-radius:8px;font-size:12px;pointer-events:none;z-index:200;animation:prompt-bounce 2s ease-in-out infinite}
@keyframes prompt-bounce{0%,100%{transform:translateY(0px)}50%{transform:translateY(-5px)}}

/* Stars and Space Effects */
.stars{position:absolute;width:100%;height:100%;background-image:radial-gradient(2px 2px at 20px 30px,#eee,transparent),radial-gradient(2px 2px at 40px 70px,rgba(255,255,255,0.8),transparent),radial-gradient(1px 1px at 90px 40px,#fff,transparent),radial-gradient(1px 1px at 130px 80px,rgba(255,255,255,0.6),transparent),radial-gradient(2px 2px at 160px 30px,#ddd,transparent);background-repeat:repeat;background-size:200px 100px;animation:twinkle 4s linear infinite}
@keyframes twinkle{from{transform:translateY(0px)}to{transform:translateY(-100px)}}
</style></head><body>
<div class="container">
<div class="header">
<div class="title">🌍 EPOCH OF ELRIA - 3D GAME ENGINE 🌍</div>
<div class="subtitle">The Dream Weaver's Heart - Complete Metaverse Experience</div>
<div id="mode">▶️ PLAY MODE</div>
</div>

<div class="world">
<div class="stars"></div>
<div class="scene-3d">

<!-- Player Character -->
<div class="player" id="player" style="top:50%;left:45%" title="You - Explorer of the Metaverse">
<div class="player-avatar"></div>
</div>

<!-- Central Earth with SVG Texture -->
<div class="earth svg-earth" onclick="selectObject('Earth')" title="Earth - The center of our universe">
🌍
</div>

<!-- Dream Weaver Characters with SVG Models -->
<div class="character xing" style="top:20%;left:60%" onclick="selectCharacter('Xing')" title="Xing - The Weaver, Master of Stories">
</div>

<div class="character xerx" style="top:30%;left:20%" onclick="selectCharacter('Xerx')" title="Xerx - The Liberator, Fighter against oppression">
</div>

<div class="character heart" style="top:60%;left:70%" onclick="selectCharacter('Heart')" title="The Heart - Catalyst of narrative potential">
</div>

<div class="character lyra" style="top:70%;left:30%" onclick="selectCharacter('Lyra')" title="Lyra - Pure Melody, Awakener of consciousness">
</div>

<div class="character the-one" style="top:10%;left:50%" onclick="selectCharacter('TheOne')" title="The One - Ancient entity of absolute order">
</div>

<!-- Platforms with SVG Textures -->
<div class="platform svg-platform" style="top:25%;left:75%" onclick="selectObject('Platform_0')" title="Platform_0 - Stone platform">
🟫
</div>

<div class="platform svg-platform" style="top:45%;left:85%" onclick="selectObject('Platform_1')" title="Platform_1 - Stone platform">
🟫
</div>

<div class="platform svg-platform" style="top:65%;left:80%" onclick="selectObject('Platform_2')" title="Platform_2 - Stone platform">
🟫
</div>

<div class="platform svg-platform" style="top:75%;left:25%" onclick="selectObject('Platform_3')" title="Platform_3 - Stone platform">
🟫
</div>

<div class="platform svg-platform" style="top:55%;left:15%" onclick="selectObject('Platform_4')" title="Platform_4 - Stone platform">
🟫
</div>

<div class="platform svg-platform" style="top:35%;left:10%" onclick="selectObject('Platform_5')" title="Platform_5 - Stone platform">
🟫
</div>

<!-- Crystals with SVG Textures -->
<div class="crystal svg-crystal" style="top:20%;left:40%" onclick="selectObject('Crystal_0')" title="Crystal_0 - Memory crystal">
💎
</div>

<div class="crystal svg-crystal" style="top:40%;left:55%" onclick="selectObject('Crystal_1')" title="Crystal_1 - Memory crystal">
💎
</div>

<div class="crystal svg-crystal" style="top:60%;left:45%" onclick="selectObject('Crystal_2')" title="Crystal_2 - Memory crystal">
💎
</div>

<div class="crystal svg-crystal" style="top:80%;left:65%" onclick="selectObject('Crystal_3')" title="Crystal_3 - Memory crystal">
💎
</div>

<!-- Metaverse Portals -->
<div class="portal" style="top:15%;left:15%" onclick="selectObject('Portal_Library')" title="Portal to Infinite Library">
🌀
</div>

<div class="portal" style="top:85%;left:85%" onclick="selectObject('Portal_Metaverse')" title="Portal to Metaverse">
🌀
</div>

</div>
</div>

<!-- Character Information Panel -->
<div class="character-panel">
<div><strong>🌟 Active Character</strong></div>
<div id="activeCharacter">Xing - The Weaver</div>
<div id="characterStats">
Narrative Power: 20 | Creativity: 18<br>
Empathy: 15 | Memory: 12 | Liberation: 8
</div>
<div id="characterAbilities">
Abilities: Weave Platform, Open Narrative Path, Manifest Concept
</div>
</div>

<!-- Camera Information -->
<div class="camera-info">
<div><strong>📷 Camera & Scene</strong></div>
<div>Position: (<span id="cameraX">0</span>, <span id="cameraY">8</span>, <span id="cameraZ">20</span>)</div>
<div>Target: (0, 0, 0)</div>
<div>3D Perspective: Active</div>
</div>

<!-- Scene Information -->
<div class="info">
<div><strong>🎬 Metaverse Scene</strong></div>
<div>Earth Sphere Sandbox</div>
<div>Characters: <span id="characterCount">5</span></div>
<div>Objects: <span id="objectCount">15</span></div>
<div>Selected: <span id="selectedObject">None</span></div>
<div>Mode: <span id="currentMode">Exploration</span></div>
</div>

<!-- Controls -->
<div class="controls">
<div class="control-group">
<span><strong>🎮 Movement:</strong></span>
<button onclick="moveCamera('w')">W ↑</button>
<button onclick="moveCamera('a')">A ←</button>
<button onclick="moveCamera('s')">S ↓</button>
<button onclick="moveCamera('d')">D →</button>
<button onclick="moveCamera('q')">Q ⬆</button>
<button onclick="moveCamera('e')">E ⬇</button>
</div>

<div class="control-group">
<button class="character-btn" onclick="switchCharacter()">Switch Character</button>
<button id="editBtn" onclick="toggleEditMode()">🔧 Edit Mode</button>
<button onclick="createPlatform()">X - Weave Platform</button>
<button onclick="openPortal()">🌀 Open Portal</button>
</div>

<div class="control-group">
<button onclick="useAbility()">⚡ Use Ability</button>
<button onclick="resetCamera()">Reset Camera</button>
<button onclick="saveScene()">💾 Save Scene</button>
</div>
</div>
</div>

<div class="notification" id="notification"></div>

<script>
// Dream Weaver Characters Database
const characters = {
    'Xing': {
        name: 'Xing',
        title: 'The Weaver',
        class: 'Reality Architect',
        stats: { narrative_power: 20, creativity: 18, empathy: 15, memory_strength: 12, liberation_force: 8 },
        abilities: ['Weave Platform', 'Open Narrative Path', 'Manifest Concept', 'Story Sanctuary', 'Reality Anchor'],
        aura: '#9966FF',
        backstory: 'Guardian of the Infinite Library, master of stories and concepts.',
        position: { x: 0, y: 2, z: 0 }
    },
    'Xerx': {
        name: 'Xerx',
        title: 'The Liberator',
        class: 'Freedom Fighter',
        stats: { liberation_force: 20, memory_strength: 18, narrative_power: 15, empathy: 12, creativity: 10 },
        abilities: ['Memory Strike', 'Break Barrier', 'Liberate Narrative', 'Reconstruct Memory', 'Resistance Aura'],
        aura: '#FF4444',
        backstory: 'Fighter against mental oppression and narrative control.',
        position: { x: 15, y: 2, z: 0 }
    },
    'Heart': {
        name: 'The Heart',
        title: 'Catalyst',
        class: 'Narrative Core',
        stats: { empathy: 20, narrative_power: 18, creativity: 16, memory_strength: 14, liberation_force: 12 },
        abilities: ['Emotional Resonance', 'Narrative Catalyst', 'Heart Bond', 'Empathic Healing', 'Unity Field'],
        aura: '#FF1493',
        backstory: 'The emotional core that connects all narratives and beings.',
        position: { x: 7, y: 3, z: 7 }
    },
    'Lyra': {
        name: 'Lyra',
        title: 'Pure Melody',
        class: 'Consciousness Awakener',
        stats: { creativity: 20, empathy: 18, narrative_power: 16, memory_strength: 14, liberation_force: 10 },
        abilities: ['Song of Awakening', 'Melody of Memory', 'Harmony of Hearts', 'Tune of Truth', 'Rhythm of Reality'],
        aura: '#99FF99',
        backstory: 'Pure melody incarnate, awakening consciousness through harmonic resonance.',
        position: { x: -10, y: 4, z: -10 }
    },
    'TheOne': {
        name: 'The One',
        title: 'Ancient Order',
        class: 'Antagonist',
        stats: { control: 25, order: 23, suppression: 20, rigidity: 18, anti_creativity: 15 },
        abilities: ['Absolute Order', 'Narrative Suppression', 'Memory Erasure', 'Reality Lock', 'Consciousness Bind'],
        aura: '#660000',
        backstory: 'Ancient entity seeking to impose absolute order on all narratives.',
        position: { x: 0, y: 50, z: 0 }
    }
};

// Game State
let gameState = {
    camera: { x: 0, y: 8, z: 20 },
    activeCharacter: 'Xing',
    editMode: false,
    selectedObject: null,
    objectCount: 15,
    characterCount: 5,
    mode: 'Exploration',
    scene3D: true,
    portalsOpen: 0
};

// Initialize the game
function initializeGame() {
    updateCharacterDisplay();
    updateCameraDisplay();
    updateObjectCount();

    // Start ambient animations
    startAmbientEffects();

    // Welcome message
    setTimeout(() => {
        showNotification('🌍 Welcome to Epoch of Elria - The Dream Weaver\'s Heart!\n\n🌟 All Dream Weaver characters are present:\n• Xing - The Weaver\n• Xerx - The Liberator\n• The Heart - Catalyst\n• Lyra - Pure Melody\n• The One - Ancient Order\n\nUse character abilities, explore the 3D metaverse!');
    }, 1500);
}

// Character Management
function selectCharacter(characterName) {
    if (characters[characterName]) {
        gameState.activeCharacter = characterName;
        updateCharacterDisplay();

        const char = characters[characterName];
        if (characterName === 'TheOne') {
            showNotification(`⚠️ You have encountered ${char.name} - ${char.title}!\n\n${char.backstory}\n\nBeware of their absolute order!`);
        } else {
            showNotification(`🌟 ${char.name} - ${char.title} selected!\n\n${char.backstory}\n\nAbilities: ${char.abilities.slice(0,3).join(', ')}`);
        }
    }
}

function switchCharacter() {
    const charNames = Object.keys(characters).filter(name => name !== 'TheOne');
    const currentIndex = charNames.indexOf(gameState.activeCharacter);
    const nextIndex = (currentIndex + 1) % charNames.length;
    selectCharacter(charNames[nextIndex]);
}

function updateCharacterDisplay() {
    const char = characters[gameState.activeCharacter];
    if (char) {
        document.getElementById('activeCharacter').textContent = `${char.name} - ${char.title}`;

        const statsText = Object.entries(char.stats)
            .map(([key, value]) => `${key.replace('_', ' ')}: ${value}`)
            .slice(0, 2)
            .join(' | ');
        document.getElementById('characterStats').innerHTML = statsText;

        document.getElementById('characterAbilities').textContent =
            `Abilities: ${char.abilities.slice(0, 3).join(', ')}`;
    }
}

// Camera and Movement
function moveCamera(direction) {
    const speed = 3;
    switch(direction) {
        case 'w': gameState.camera.z -= speed; break;
        case 's': gameState.camera.z += speed; break;
        case 'a': gameState.camera.x -= speed; break;
        case 'd': gameState.camera.x += speed; break;
        case 'q': gameState.camera.y += speed; break;
        case 'e': gameState.camera.y -= speed; break;
    }
    updateCameraDisplay();
    updateScenePerspective();
    showNotification(`📷 Camera moved ${direction.toUpperCase()} - 3D perspective updated`);
}

function updateCameraDisplay() {
    document.getElementById('cameraX').textContent = Math.round(gameState.camera.x);
    document.getElementById('cameraY').textContent = Math.round(gameState.camera.y);
    document.getElementById('cameraZ').textContent = Math.round(gameState.camera.z);
}

function updateScenePerspective() {
    const scene = document.querySelector('.scene-3d');
    if (scene) {
        const rotateY = gameState.camera.x * 0.5;
        const rotateX = gameState.camera.y * 0.3;
        scene.style.transform = `rotateY(${rotateY}deg) rotateX(${rotateX}deg)`;
    }
}

// Edit Mode and Building
function toggleEditMode() {
    gameState.editMode = !gameState.editMode;
    const modeDisplay = document.getElementById('mode');
    const editBtn = document.getElementById('editBtn');

    if (gameState.editMode) {
        modeDisplay.textContent = '🔧 EDIT MODE';
        modeDisplay.style.color = '#FF6B35';
        editBtn.textContent = '▶️ Play Mode';
        editBtn.classList.add('edit-mode');
        gameState.mode = 'Building';
        showNotification('🔧 Edit Mode Activated!\n\nYou can now:\n• Create platforms with X\n• Open portals\n• Modify the metaverse\n• Use character abilities');
    } else {
        modeDisplay.textContent = '▶️ PLAY MODE';
        modeDisplay.style.color = '#FFD700';
        editBtn.textContent = '🔧 Edit Mode';
        editBtn.classList.remove('edit-mode');
        gameState.mode = 'Exploration';
        showNotification('▶️ Play Mode Activated!\n\nExplore the metaverse and interact with characters!');
    }

    document.getElementById('currentMode').textContent = gameState.mode;
}

// Platform Creation (Xing's Ability)
function createPlatform() {
    const char = characters[gameState.activeCharacter];

    if (char.name === 'Xing') {
        const gameWorld = document.querySelector('.world');
        const newPlatform = document.createElement('div');
        newPlatform.className = 'platform svg-platform';
        newPlatform.style.top = Math.random() * 70 + 15 + '%';
        newPlatform.style.left = Math.random() * 70 + 15 + '%';
        newPlatform.innerHTML = '🟫';
        newPlatform.title = `Woven_Platform_${Date.now()}`;
        newPlatform.onclick = () => selectObject(newPlatform.title);

        gameWorld.appendChild(newPlatform);
        gameState.objectCount++;
        updateObjectCount();

        showNotification('✨ Xing weaves a new platform from pure narrative energy!\n\n"Reality bends to the power of story..."');
    } else {
        showNotification(`❌ Only Xing can weave platforms!\n\nCurrent character: ${char.name} - ${char.title}\n\nSwitch to Xing to use this ability.`);
    }
}

// Portal System
function openPortal() {
    const char = characters[gameState.activeCharacter];

    if (gameState.editMode) {
        const gameWorld = document.querySelector('.world');
        const newPortal = document.createElement('div');
        newPortal.className = 'portal';
        newPortal.style.top = Math.random() * 70 + 15 + '%';
        newPortal.style.left = Math.random() * 70 + 15 + '%';
        newPortal.innerHTML = '🌀';
        newPortal.title = `Portal_${Date.now()}`;
        newPortal.onclick = () => selectObject(newPortal.title);

        gameWorld.appendChild(newPortal);
        gameState.portalsOpen++;
        gameState.objectCount++;
        updateObjectCount();

        showNotification(`🌀 ${char.name} opens a portal to another dimension!\n\nPortals active: ${gameState.portalsOpen}\n\n"The metaverse expands..."`);
    } else {
        showNotification('🌀 Portal magic requires Edit Mode!\n\nActivate Edit Mode first to open portals.');
    }
}

// Character Abilities
function useAbility() {
    const char = characters[gameState.activeCharacter];
    const ability = char.abilities[Math.floor(Math.random() * char.abilities.length)];

    let effect = '';
    switch(char.name) {
        case 'Xing':
            effect = `📚 ${ability}!\n\nXing weaves reality through narrative power.\nThe story world reshapes itself...`;
            break;
        case 'Xerx':
            effect = `⚔️ ${ability}!\n\nXerx breaks through mental barriers.\nFreedom spreads through the metaverse...`;
            break;
        case 'Heart':
            effect = `💖 ${ability}!\n\nThe Heart's emotional resonance touches all beings.\nEmpathy flows through the narrative...`;
            break;
        case 'Lyra':
            effect = `🎵 ${ability}!\n\nLyra's pure melody awakens consciousness.\nHarmonic frequencies restore awareness...`;
            break;
        case 'TheOne':
            effect = `⚫ ${ability}!\n\nThe One imposes absolute order.\nChaos is suppressed, creativity diminished...`;
            break;
    }

    showNotification(`🌟 ${char.name} uses ${ability}!\n\n${effect}`);

    // Visual effect
    const charElement = document.querySelector(`.${char.name.toLowerCase()}`);
    if (charElement) {
        charElement.style.filter = 'brightness(1.5) drop-shadow(0 0 20px ' + char.aura + ')';
        setTimeout(() => {
            charElement.style.filter = '';
        }, 2000);
    }
}

// Object Selection and Interaction
function selectObject(objectName) {
    gameState.selectedObject = objectName;
    document.getElementById('selectedObject').textContent = objectName;

    if (gameState.editMode) {
        showNotification(`🔧 Selected: ${objectName}\n\nEdit Mode: Ready for modification!\n\nUse character abilities to interact.`);
    } else {
        showNotification(`👁️ Viewing: ${objectName}\n\nSwitch to Edit Mode to modify objects.`);
    }
}

// Utility Functions
function updateObjectCount() {
    document.getElementById('objectCount').textContent = gameState.objectCount;
    document.getElementById('characterCount').textContent = gameState.characterCount;
}

function resetCamera() {
    gameState.camera = { x: 0, y: 8, z: 20 };
    updateCameraDisplay();
    updateScenePerspective();
    showNotification('📷 Camera reset to default position\n\n3D perspective restored');
}

function saveScene() {
    const sceneData = {
        characters: Object.keys(characters),
        objects: gameState.objectCount,
        camera: gameState.camera,
        mode: gameState.mode,
        timestamp: new Date().toISOString()
    };

    localStorage.setItem('epochOfElriaScene', JSON.stringify(sceneData));
    showNotification('💾 Scene saved to local storage!\n\nAll character positions and objects preserved.\n\nScene can be restored on next visit.');
}

function startAmbientEffects() {
    // Rotate the 3D scene slowly
    setInterval(() => {
        const scene = document.querySelector('.scene-3d');
        if (scene && !gameState.editMode) {
            const currentTransform = scene.style.transform || '';
            const rotateMatch = currentTransform.match(/rotateY\(([-\d.]+)deg\)/);
            const currentRotation = rotateMatch ? parseFloat(rotateMatch[1]) : 0;
            const newRotation = (currentRotation + 0.1) % 360;

            scene.style.transform = currentTransform.replace(
                /rotateY\([-\d.]+deg\)/,
                `rotateY(${newRotation}deg)`
            ) || `rotateY(${newRotation}deg)`;
        }
    }, 100);

    // Character aura effects
    setInterval(() => {
        Object.keys(characters).forEach(charName => {
            const element = document.querySelector(`.${charName.toLowerCase()}`);
            if (element && charName !== gameState.activeCharacter) {
                const char = characters[charName];
                element.style.filter = `drop-shadow(0 0 ${5 + Math.random() * 10}px ${char.aura})`;
            }
        });
    }, 2000);
}

function showNotification(message) {
    const notification = document.getElementById('notification');
    notification.innerHTML = message.replace(/\n/g, '<br>');
    notification.style.display = 'block';

    setTimeout(() => {
        notification.style.display = 'none';
    }, 4000);
}

// Keyboard Controls
document.addEventListener('keydown', (e) => {
    switch(e.key.toLowerCase()) {
        case 'w': moveCamera('w'); break;
        case 'a': moveCamera('a'); break;
        case 's': moveCamera('s'); break;
        case 'd': moveCamera('d'); break;
        case 'q': moveCamera('q'); break;
        case 'e':
            if (e.ctrlKey) {
                toggleEditMode();
            } else {
                moveCamera('e');
            }
            break;
        case 'x': createPlatform(); break;
        case 'c': switchCharacter(); break;
        case 'z': useAbility(); break;
        case 'p': openPortal(); break;
        case ' ': resetCamera(); e.preventDefault(); break;
        case 'escape':
            showNotification('👋 Thanks for exploring Epoch of Elria!\n\nThe Dream Weaver\'s Heart continues...');
            break;
    }
});

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', initializeGame);

// Legacy functions for compatibility
function select(obj) { selectObject(obj); }
function toggleEdit() { toggleEditMode(); }
function create() { createPlatform(); }
function move(dir) { moveCamera(dir); }
function reset() { resetCamera(); }
</script>
</body></html>
<div class="container">
<div class="header"><div class="title">🌍 EPOCH OF ELRIA GAME ENGINE 🌍</div><div id="mode">▶️ PLAY MODE</div></div>
<div class="world">
<div class="earth" onclick="select('Earth')">🌍</div>
<div class="platform" style="top:30%;left:70%" onclick="select('Platform_0')">🟫</div>
<div class="platform" style="top:45%;left:80%" onclick="select('Platform_1')">🟫</div>
<div class="platform" style="top:65%;left:75%" onclick="select('Platform_2')">🟫</div>
<div class="platform" style="top:70%;left:30%" onclick="select('Platform_3')">🟫</div>
<div class="platform" style="top:55%;left:20%" onclick="select('Platform_4')">��</div>
<div class="platform" style="top:35%;left:25%" onclick="select('Platform_5')">🟫</div>
<div class="crystal" style="top:25%;left:40%" onclick="select('Crystal_0')">💎</div>
<div class="crystal" style="top:40%;left:55%" onclick="select('Crystal_1')">💎</div>
<div class="crystal" style="top:60%;left:45%" onclick="select('Crystal_2')">💎</div>
<div class="crystal" style="top:75%;left:60%" onclick="select('Crystal_3')">💎</div>
</div>
<div class="info"><div><strong>🎬 Scene</strong></div><div>Earth Sphere Sandbox</div><div>Objects: <span id="count">11</span></div><div>Selected: <span id="selected">None</span></div></div>
<div class="controls">
<div><button onclick="move('w')">W ↑</button><button onclick="move('a')">A ←</button><button onclick="move('s')">S ↓</button><button onclick="move('d')">D →</button></div>
<div><button id="editBtn" onclick="toggleEdit()">E - Edit Mode</button><button onclick="create()">X - Create Platform</button><button onclick="reset()">Reset</button></div>
</div>
</div>
<script>
let editMode=false,selected=null,count=11;
function select(obj){selected=obj;document.getElementById('selected').textContent=obj;alert(editMode?'🔧 Selected: '+obj:'👁️ Viewing: '+obj)}
function toggleEdit(){editMode=!editMode;document.getElementById('mode').textContent=editMode?'🔧 EDIT MODE':'▶️ PLAY MODE';document.getElementById('editBtn').textContent=editMode?'E - Play Mode':'E - Edit Mode';document.getElementById('editBtn').className=editMode?'edit-mode':'';alert(editMode?'🔧 Edit Mode Activated!':'▶️ Play Mode Activated!')}
function create(){const w=document.querySelector('.world'),p=document.createElement('div');p.className='platform';p.style.top=Math.random()*70+15+'%';p.style.left=Math.random()*70+15+'%';p.innerHTML='🟫';p.onclick=()=>select('Woven_'+Date.now());w.appendChild(p);count++;document.getElementById('count').textContent=count;alert('✨ Reality Weaving Activated! New platform created!')}
function move(dir){alert('📷 Camera moved '+dir.toUpperCase())}
function reset(){alert('📷 Camera reset to default position')}
document.addEventListener('keydown',e=>{switch(e.key.toLowerCase()){case 'w':move('w');break;case 'a':move('a');break;case 's':move('s');break;case 'd':move('d');break;case 'e':toggleEdit();break;case 'x':create();break;case ' ':reset();e.preventDefault();break}});
setTimeout(()=>alert('🌍 Welcome to Epoch of Elria Game Engine!\nUse WASD, E for Edit Mode, X to create platforms!'),1000);
</script></body></html>
