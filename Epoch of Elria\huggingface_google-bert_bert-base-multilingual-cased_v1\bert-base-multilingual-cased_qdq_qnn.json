{"input_model": {"type": "HfModel", "model_path": "google-bert/bert-base-multilingual-cased", "task": "feature-extraction"}, "systems": {"qnn_system": {"type": "LocalSystem", "accelerators": [{"device": "npu", "execution_providers": ["QNNExecutionProvider"]}]}}, "data_configs": [{"name": "quantization_data_config", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "load_dataset_config": {"data_name": "facebook/xnli", "subset": "en", "split": "validation"}, "pre_process_data_config": {"input_cols": ["premise"], "padding": "max_length", "max_length": 128, "max_samples": 10}, "dataloader_config": {"batch_size": 1}}, {"name": "evaluation_data_config", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "load_dataset_config": {"data_name": "facebook/xnli", "subset": "en", "split": "validation"}, "pre_process_data_config": {"input_cols": ["premise"], "padding": "max_length", "max_length": 128, "max_samples": 10}, "dataloader_config": {"batch_size": 1}}], "evaluators": {"common_evaluator": {"metrics": [{"name": "latency", "type": "latency", "data_config": "evaluation_data_config", "sub_types": [{"name": "avg", "priority": 1, "goal": {"type": "percent-min-improvement", "value": 0.1}}, {"name": "max"}, {"name": "min"}]}, {"name": "throughput", "type": "throughput", "data_config": "evaluation_data_config", "sub_types": [{"name": "avg"}, {"name": "max"}, {"name": "min"}]}]}}, "passes": {"conversion": {"type": "OnnxConversion", "target_opset": 20, "dynamic": true, "use_dynamo_exporter": false, "save_as_external_data": true}, "to_fixed_shape": {"type": "DynamicToFixedShape", "dim_param": ["batch_size", "sequence_length"], "dim_value": [1, 128]}, "surgery": {"type": "GraphSurgeries", "surgeries": [{"surgeon": "ReplaceAttentionMaskValue", "replacement": -100.0}, {"surgeon": "MatMulAddToGemm"}]}, "transformer_optimizer": {"type": "OrtTransformersOptimization", "model_type": "bert", "opt_level": 1, "optimization_options": {"enable_gelu": true, "enable_bias_gelu": false, "enable_layer_norm": true, "enable_skip_layer_norm": false, "enable_bias_skip_layer_norm": false, "enable_attention": false}}, "OnnxQuantization": {"type": "OnnxStaticQuantization", "data_config": "quantization_data_config", "quant_preprocess": true, "activation_type": "uint16", "precision": "uint8", "save_as_external_data": true}}, "host": "qnn_system", "target": "qnn_system", "evaluator": "common_evaluator", "cache_dir": "cache", "output_dir": "model/google_bert", "evaluate_input_model": false}